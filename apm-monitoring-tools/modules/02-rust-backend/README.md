# Module 2: Basic Rust Backend Foundation

## 🎯 Learning Objectives

In this module, you will:
- Build your first HTTP server using Axum framework
- Understand Rust ownership, borrowing, and lifetimes
- Implement proper error handling patterns
- Create RESTful API endpoints
- Learn async programming in Rust
- Write your first unit tests

## 🦀 Deep Dive: Rust Ownership System

Before we build our server, let's understand Rust's most important concept: ownership.

### Memory Management: Rust vs C#

**C# (Garbage Collected):**
```csharp
public class MetricData 
{
    public string Name { get; set; }
    public double Value { get; set; }
}

public void ProcessMetrics()
{
    var metrics = new List<MetricData>();
    metrics.Add(new MetricData { Name = "CPU", Value = 85.5 });
    
    // GC will clean up when out of scope
    SendToDatabase(metrics);
    // metrics still accessible here
}
```

**Rust (Ownership):**
```rust
#[derive(Debug, Clone)]
pub struct MetricData {
    pub name: String,
    pub value: f64,
}

pub fn process_metrics() {
    let mut metrics = Vec::new();
    metrics.push(MetricData {
        name: "CPU".to_string(),
        value: 85.5,
    });
    
    // This moves ownership to the function
    send_to_database(metrics);
    // metrics is no longer accessible here!
    
    // To keep using it, we can clone or borrow:
    let metrics = vec![MetricData { name: "CPU".to_string(), value: 85.5 }];
    send_to_database(metrics.clone()); // Clone the data
    println!("{:?}", metrics); // Still accessible
    
    // Or borrow it:
    send_to_database_ref(&metrics); // Borrow instead of move
    println!("{:?}", metrics); // Still accessible
}

fn send_to_database(data: Vec<MetricData>) {
    // Takes ownership of data
}

fn send_to_database_ref(data: &Vec<MetricData>) {
    // Borrows data, doesn't take ownership
}
```

### Why This Matters for APM Systems

APM systems handle massive amounts of data. Rust's ownership system ensures:
- **No memory leaks**: Memory is freed automatically when owners go out of scope
- **No double-free errors**: Can't accidentally free the same memory twice
- **Thread safety**: Ownership rules prevent data races at compile time

## 🌐 Building Our HTTP Server with Axum

### Why Axum Over Other Frameworks?

**Axum vs Actix-web vs Warp:**

| Feature | Axum | Actix-web | Warp |
|---------|------|-----------|------|
| Performance | Excellent | Excellent | Good |
| Learning Curve | Moderate | Steep | Steep |
| Type Safety | Excellent | Good | Excellent |
| Ecosystem | Growing | Mature | Moderate |
| Async Support | Native | Native | Native |

**We choose Axum because:**
- Built by the Tokio team (async runtime experts)
- Excellent type safety with extractors
- Clean, composable API design
- Great error handling support

### Creating Our First Server

Let's build our APM backend step by step:

```rust
// src/main.rs
use axum::{
    extract::{Path, Query, State},
    http::StatusCode,
    response::Json,
    routing::{get, post},
    Router,
};
use serde::{Deserialize, Serialize};
use std::{collections::HashMap, sync::Arc};
use tokio::sync::RwLock;
use tracing::{info, warn};

// Our application state
#[derive(Clone)]
pub struct AppState {
    pub metrics: Arc<RwLock<Vec<MetricData>>>,
}

// Data structures
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MetricData {
    pub id: String,
    pub name: String,
    pub value: f64,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub tags: HashMap<String, String>,
}

#[derive(Deserialize)]
pub struct CreateMetricRequest {
    pub name: String,
    pub value: f64,
    pub tags: Option<HashMap<String, String>>,
}

#[derive(Deserialize)]
pub struct MetricQuery {
    pub limit: Option<usize>,
    pub name: Option<String>,
}

#[tokio::main]
async fn main() {
    // Initialize tracing
    tracing_subscriber::fmt()
        .with_max_level(tracing::Level::DEBUG)
        .init();

    info!("🚀 Starting APM Backend Server");

    // Initialize application state
    let state = AppState {
        metrics: Arc::new(RwLock::new(Vec::new())),
    };

    // Build our router
    let app = Router::new()
        .route("/", get(root))
        .route("/health", get(health_check))
        .route("/api/v1/metrics", get(get_metrics).post(create_metric))
        .route("/api/v1/metrics/:id", get(get_metric_by_id))
        .with_state(state);

    // Start server
    let listener = tokio::net::TcpListener::bind("127.0.0.1:8080")
        .await
        .unwrap();
    
    info!("🌐 Server listening on http://127.0.0.1:8080");
    axum::serve(listener, app).await.unwrap();
}
```

### Understanding Async in Rust

**C# Async/Await:**
```csharp
public async Task<List<MetricData>> GetMetricsAsync()
{
    var data = await database.QueryAsync("SELECT * FROM metrics");
    return data.ToList();
}
```

**Rust Async/Await:**
```rust
pub async fn get_metrics() -> Result<Vec<MetricData>, DatabaseError> {
    let data = database.query("SELECT * FROM metrics").await?;
    Ok(data)
}
```

**Key Differences:**
- Rust's `async` functions return `Future`s, not `Task`s
- Error handling is explicit with `Result<T, E>`
- No runtime exceptions - all errors must be handled

## 🔧 Implementing API Endpoints

Let's implement our core endpoints:

```rust
// Handler functions
async fn root() -> Json<serde_json::Value> {
    Json(serde_json::json!({
        "service": "APM Monitoring Backend",
        "version": "0.1.0",
        "status": "running"
    }))
}

async fn health_check() -> Json<serde_json::Value> {
    Json(serde_json::json!({
        "status": "healthy",
        "timestamp": chrono::Utc::now(),
        "uptime": "TODO: implement uptime tracking"
    }))
}

async fn get_metrics(
    State(state): State<AppState>,
    Query(params): Query<MetricQuery>,
) -> Result<Json<Vec<MetricData>>, StatusCode> {
    let metrics = state.metrics.read().await;
    
    let mut filtered_metrics: Vec<MetricData> = metrics
        .iter()
        .filter(|metric| {
            if let Some(ref name) = params.name {
                metric.name.contains(name)
            } else {
                true
            }
        })
        .cloned()
        .collect();

    // Apply limit
    if let Some(limit) = params.limit {
        filtered_metrics.truncate(limit);
    }

    Ok(Json(filtered_metrics))
}

async fn create_metric(
    State(state): State<AppState>,
    Json(payload): Json<CreateMetricRequest>,
) -> Result<Json<MetricData>, StatusCode> {
    let metric = MetricData {
        id: uuid::Uuid::new_v4().to_string(),
        name: payload.name,
        value: payload.value,
        timestamp: chrono::Utc::now(),
        tags: payload.tags.unwrap_or_default(),
    };

    // Add to our in-memory store
    let mut metrics = state.metrics.write().await;
    metrics.push(metric.clone());
    
    info!("Created new metric: {}", metric.name);
    Ok(Json(metric))
}

async fn get_metric_by_id(
    State(state): State<AppState>,
    Path(id): Path<String>,
) -> Result<Json<MetricData>, StatusCode> {
    let metrics = state.metrics.read().await;
    
    match metrics.iter().find(|m| m.id == id) {
        Some(metric) => Ok(Json(metric.clone())),
        None => {
            warn!("Metric not found: {}", id);
            Err(StatusCode::NOT_FOUND)
        }
    }
}
```

## 🚨 Error Handling in Rust

Rust's error handling is explicit and powerful. Let's create a proper error system:

```rust
// src/errors.rs
use axum::{
    http::StatusCode,
    response::{IntoResponse, Response},
    Json,
};
use serde_json::json;
use thiserror::Error;

#[derive(Error, Debug)]
pub enum AppError {
    #[error("Database error: {0}")]
    Database(#[from] sqlx::Error),
    
    #[error("Validation error: {0}")]
    Validation(String),
    
    #[error("Not found: {0}")]
    NotFound(String),
    
    #[error("Internal server error")]
    Internal,
}

impl IntoResponse for AppError {
    fn into_response(self) -> Response {
        let (status, error_message) = match self {
            AppError::Database(err) => {
                tracing::error!("Database error: {}", err);
                (StatusCode::INTERNAL_SERVER_ERROR, "Database error")
            }
            AppError::Validation(msg) => (StatusCode::BAD_REQUEST, msg.as_str()),
            AppError::NotFound(msg) => (StatusCode::NOT_FOUND, msg.as_str()),
            AppError::Internal => {
                tracing::error!("Internal server error");
                (StatusCode::INTERNAL_SERVER_ERROR, "Internal server error")
            }
        };

        let body = Json(json!({
            "error": error_message,
            "status": status.as_u16()
        }));

        (status, body).into_response()
    }
}

// Result type alias for convenience
pub type Result<T> = std::result::Result<T, AppError>;
```

## 🧪 Writing Unit Tests

Testing is crucial for APM systems. Let's write comprehensive tests:

```rust
// src/tests/mod.rs
#[cfg(test)]
mod tests {
    use super::*;
    use axum::{
        body::Body,
        http::{Request, StatusCode},
    };
    use tower::ServiceExt; // for `oneshot`

    #[tokio::test]
    async fn test_health_check() {
        let app = create_test_app().await;

        let response = app
            .oneshot(Request::builder().uri("/health").body(Body::empty()).unwrap())
            .await
            .unwrap();

        assert_eq!(response.status(), StatusCode::OK);
    }

    #[tokio::test]
    async fn test_create_metric() {
        let app = create_test_app().await;

        let metric_request = json!({
            "name": "cpu_usage",
            "value": 85.5,
            "tags": {
                "host": "server-01",
                "region": "us-east-1"
            }
        });

        let response = app
            .oneshot(
                Request::builder()
                    .method("POST")
                    .uri("/api/v1/metrics")
                    .header("content-type", "application/json")
                    .body(Body::from(metric_request.to_string()))
                    .unwrap(),
            )
            .await
            .unwrap();

        assert_eq!(response.status(), StatusCode::OK);
    }

    async fn create_test_app() -> Router {
        let state = AppState {
            metrics: Arc::new(RwLock::new(Vec::new())),
        };

        Router::new()
            .route("/health", get(health_check))
            .route("/api/v1/metrics", post(create_metric))
            .with_state(state)
    }
}
```

## 📊 Performance Considerations

### Why Rust is Fast for APM Systems

```rust
// Rust - Zero-cost abstractions
pub fn process_metrics_batch(metrics: &[MetricData]) -> f64 {
    metrics
        .iter()                    // Iterator - no heap allocation
        .filter(|m| m.value > 0.0) // Lazy evaluation
        .map(|m| m.value)          // No intermediate collections
        .sum()                     // Single pass through data
}
```

**Compared to C#:**
```csharp
// C# - Multiple allocations and passes
public double ProcessMetricsBatch(List<MetricData> metrics)
{
    return metrics
        .Where(m => m.Value > 0.0)  // Creates intermediate IEnumerable
        .Select(m => m.Value)       // Creates another intermediate
        .Sum();                     // Final pass
}
```

## 🎯 Module 2 Exercises

1. **Basic Server**: Run the setup script and start your server
2. **API Testing**: Use curl or Postman to test all endpoints
3. **Error Handling**: Add validation for metric names (no empty strings)
4. **Unit Tests**: Write tests for the get_metrics endpoint
5. **Performance**: Add timing logs to measure request processing time

### Exercise Solutions

**1. Testing with curl:**
```bash
# Health check
curl http://localhost:8080/health

# Create a metric
curl -X POST http://localhost:8080/api/v1/metrics \
  -H "Content-Type: application/json" \
  -d '{"name": "cpu_usage", "value": 85.5, "tags": {"host": "server-01"}}'

# Get all metrics
curl http://localhost:8080/api/v1/metrics

# Get metrics with filter
curl "http://localhost:8080/api/v1/metrics?name=cpu&limit=10"
```

## 🔍 What's Next?

In Module 3, we'll:
- Connect to PostgreSQL database
- Implement proper data persistence
- Learn about database migrations
- Explore async database operations with SQLx
- Handle connection pooling

The foundation we've built here gives us a solid HTTP server with proper error handling and testing. Next, we'll make it production-ready with persistent storage!

## 📚 Key Takeaways

1. **Ownership**: Rust's ownership system prevents memory bugs at compile time
2. **Async**: Rust's async model is efficient for I/O-heavy applications like APM
3. **Error Handling**: Explicit error handling makes systems more reliable
4. **Testing**: Unit tests are first-class citizens in Rust
5. **Performance**: Zero-cost abstractions mean fast code without sacrificing safety

Ready to add database persistence? Let's move to Module 3!

## 📁 Complete Code Files

The complete implementation for this module is available in the following files:
- `src/main.rs` - Main server implementation
- `src/errors.rs` - Error handling system
- `src/models.rs` - Data structures
- `src/handlers.rs` - API endpoint handlers
- `tests/integration_tests.rs` - Integration tests

Run the provided setup script to generate all files automatically!
