#!/bin/bash

# Module 2: Rust Backend Foundation Setup Script
# Creates the complete backend structure with all necessary files

set -e

echo "🦀 Setting up Module 2: Rust Backend Foundation"
echo "=============================================="

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

print_status() {
    echo -e "${GREEN}✓${NC} $1"
}

print_info() {
    echo -e "${YELLOW}ℹ${NC} $1"
}

# Ensure we're in the right directory
if [ ! -d "backend" ]; then
    echo "Creating backend directory..."
    mkdir -p backend/src
fi

cd backend

# Create Cargo.toml if it doesn't exist
if [ ! -f "Cargo.toml" ]; then
    print_info "Creating Cargo.toml..."
    cat > Cargo.toml << 'EOF'
[package]
name = "apm-backend"
version = "0.1.0"
edition = "2021"

[dependencies]
# Web framework
axum = "0.7"
tokio = { version = "1.0", features = ["full"] }
tower = "0.4"
tower-http = { version = "0.5", features = ["cors", "trace"] }

# Serialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# Database (for future modules)
sqlx = { version = "0.7", features = ["runtime-tokio-rustls", "postgres", "chrono", "uuid"] }

# Utilities
chrono = { version = "0.4", features = ["serde"] }
uuid = { version = "1.0", features = ["v4", "serde"] }
tracing = "0.1"
tracing-subscriber = "0.3"

# Environment
dotenvy = "0.15"

# Error handling
anyhow = "1.0"
thiserror = "1.0"

[dev-dependencies]
tokio-test = "0.4"
tower = { version = "0.4", features = ["util"] }
hyper = { version = "1.0", features = ["full"] }
EOF
    print_status "Cargo.toml created"
fi

# Create lib.rs for public modules
print_info "Creating src/lib.rs..."
cat > src/lib.rs << 'EOF'
pub mod errors;
pub mod handlers;
pub mod models;
EOF

# Create main.rs
print_info "Creating src/main.rs..."
cat > src/main.rs << 'EOF'
mod errors;
mod handlers;
mod models;

use axum::{
    routing::{get, post},
    Router,
};
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::{info, Level};
use tracing_subscriber;

use crate::models::AppState;
use crate::handlers::*;

#[tokio::main]
async fn main() {
    // Initialize tracing
    tracing_subscriber::fmt()
        .with_max_level(Level::DEBUG)
        .init();

    info!("🚀 Starting APM Backend Server");

    // Initialize application state
    let state = AppState {
        metrics: Arc::new(RwLock::new(Vec::new())),
    };

    // Build our router
    let app = Router::new()
        .route("/", get(root))
        .route("/health", get(health_check))
        .route("/api/v1/metrics", get(get_metrics).post(create_metric))
        .route("/api/v1/metrics/:id", get(get_metric_by_id))
        .with_state(state);

    // Start server
    let listener = tokio::net::TcpListener::bind("127.0.0.1:8080")
        .await
        .unwrap();
    
    info!("🌐 Server listening on http://127.0.0.1:8080");
    info!("📊 Try: curl http://127.0.0.1:8080/health");
    
    axum::serve(listener, app).await.unwrap();
}
EOF

# Create models.rs
print_info "Creating src/models.rs..."
cat > src/models.rs << 'EOF'
use serde::{Deserialize, Serialize};
use std::{collections::HashMap, sync::Arc};
use tokio::sync::RwLock;

// Application state shared across handlers
#[derive(Clone)]
pub struct AppState {
    pub metrics: Arc<RwLock<Vec<MetricData>>>,
}

// Core metric data structure
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MetricData {
    pub id: String,
    pub name: String,
    pub value: f64,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub tags: HashMap<String, String>,
}

// Request/Response DTOs
#[derive(Deserialize)]
pub struct CreateMetricRequest {
    pub name: String,
    pub value: f64,
    pub tags: Option<HashMap<String, String>>,
}

#[derive(Deserialize)]
pub struct MetricQuery {
    pub limit: Option<usize>,
    pub name: Option<String>,
}

#[derive(Serialize)]
pub struct ApiResponse<T> {
    pub success: bool,
    pub data: Option<T>,
    pub message: Option<String>,
}

impl<T> ApiResponse<T> {
    pub fn success(data: T) -> Self {
        Self {
            success: true,
            data: Some(data),
            message: None,
        }
    }

    pub fn error(message: String) -> ApiResponse<()> {
        ApiResponse {
            success: false,
            data: None,
            message: Some(message),
        }
    }
}
EOF

# Create handlers.rs
print_info "Creating src/handlers.rs..."
cat > src/handlers.rs << 'EOF'
use axum::{
    extract::{Path, Query, State},
    http::StatusCode,
    response::Json,
};
use serde_json::{json, Value};
use tracing::{info, warn};

use crate::errors::{AppError, Result};
use crate::models::*;

pub async fn root() -> Json<Value> {
    Json(json!({
        "service": "APM Monitoring Backend",
        "version": "0.1.0",
        "status": "running",
        "endpoints": {
            "health": "/health",
            "metrics": "/api/v1/metrics",
            "metric_by_id": "/api/v1/metrics/{id}"
        }
    }))
}

pub async fn health_check() -> Json<Value> {
    Json(json!({
        "status": "healthy",
        "timestamp": chrono::Utc::now(),
        "service": "apm-backend",
        "version": "0.1.0"
    }))
}

pub async fn get_metrics(
    State(state): State<AppState>,
    Query(params): Query<MetricQuery>,
) -> Result<Json<ApiResponse<Vec<MetricData>>>> {
    let metrics = state.metrics.read().await;
    
    let mut filtered_metrics: Vec<MetricData> = metrics
        .iter()
        .filter(|metric| {
            if let Some(ref name) = params.name {
                metric.name.contains(name)
            } else {
                true
            }
        })
        .cloned()
        .collect();

    // Apply limit
    if let Some(limit) = params.limit {
        filtered_metrics.truncate(limit);
    }

    info!("Retrieved {} metrics", filtered_metrics.len());
    Ok(Json(ApiResponse::success(filtered_metrics)))
}

pub async fn create_metric(
    State(state): State<AppState>,
    Json(payload): Json<CreateMetricRequest>,
) -> Result<Json<ApiResponse<MetricData>>> {
    // Validation
    if payload.name.trim().is_empty() {
        return Err(AppError::Validation("Metric name cannot be empty".to_string()));
    }

    let metric = MetricData {
        id: uuid::Uuid::new_v4().to_string(),
        name: payload.name.trim().to_string(),
        value: payload.value,
        timestamp: chrono::Utc::now(),
        tags: payload.tags.unwrap_or_default(),
    };

    // Add to our in-memory store
    let mut metrics = state.metrics.write().await;
    metrics.push(metric.clone());
    
    info!("Created new metric: {} = {}", metric.name, metric.value);
    Ok(Json(ApiResponse::success(metric)))
}

pub async fn get_metric_by_id(
    State(state): State<AppState>,
    Path(id): Path<String>,
) -> Result<Json<ApiResponse<MetricData>>> {
    let metrics = state.metrics.read().await;
    
    match metrics.iter().find(|m| m.id == id) {
        Some(metric) => {
            info!("Found metric: {}", id);
            Ok(Json(ApiResponse::success(metric.clone())))
        }
        None => {
            warn!("Metric not found: {}", id);
            Err(AppError::NotFound(format!("Metric with id {} not found", id)))
        }
    }
}
EOF

# Create errors.rs
print_info "Creating src/errors.rs..."
cat > src/errors.rs << 'EOF'
use axum::{
    http::StatusCode,
    response::{IntoResponse, Response},
    Json,
};
use serde_json::json;
use thiserror::Error;

#[derive(Error, Debug)]
pub enum AppError {
    #[error("Database error: {0}")]
    Database(#[from] sqlx::Error),

    #[error("Validation error: {0}")]
    Validation(String),

    #[error("Not found: {0}")]
    NotFound(String),

    #[error("Internal server error")]
    Internal,

    #[error("Unauthorized")]
    Unauthorized,

    #[error("Forbidden")]
    Forbidden,
}

impl IntoResponse for AppError {
    fn into_response(self) -> Response {
        let (status, error_message) = match self {
            AppError::Database(err) => {
                tracing::error!("Database error: {}", err);
                (StatusCode::INTERNAL_SERVER_ERROR, "Database error occurred")
            }
            AppError::Validation(msg) => (StatusCode::BAD_REQUEST, msg.as_str()),
            AppError::NotFound(msg) => (StatusCode::NOT_FOUND, msg.as_str()),
            AppError::Internal => {
                tracing::error!("Internal server error");
                (StatusCode::INTERNAL_SERVER_ERROR, "Internal server error")
            }
            AppError::Unauthorized => (StatusCode::UNAUTHORIZED, "Unauthorized"),
            AppError::Forbidden => (StatusCode::FORBIDDEN, "Forbidden"),
        };

        let body = Json(json!({
            "success": false,
            "error": error_message,
            "status": status.as_u16(),
            "timestamp": chrono::Utc::now()
        }));

        (status, body).into_response()
    }
}

// Result type alias for convenience
pub type Result<T> = std::result::Result<T, AppError>;
EOF

print_status "All source files created"

# Create tests directory and integration tests
mkdir -p tests
print_info "Creating tests/integration_tests.rs..."
cat > tests/integration_tests.rs << 'EOF'
use axum::{
    body::Body,
    http::{Request, StatusCode},
    Router,
};
use serde_json::json;
use std::sync::Arc;
use tokio::sync::RwLock;
use tower::ServiceExt;

// Import from our main crate
use apm_backend::{handlers::*, models::*};

async fn create_test_app() -> Router {
    let state = AppState {
        metrics: Arc::new(RwLock::new(Vec::new())),
    };

    Router::new()
        .route("/health", axum::routing::get(health_check))
        .route("/api/v1/metrics", axum::routing::get(get_metrics).post(create_metric))
        .route("/api/v1/metrics/:id", axum::routing::get(get_metric_by_id))
        .with_state(state)
}

#[tokio::test]
async fn test_health_check() {
    let app = create_test_app().await;

    let response = app
        .oneshot(Request::builder().uri("/health").body(Body::empty()).unwrap())
        .await
        .unwrap();

    assert_eq!(response.status(), StatusCode::OK);
}

#[tokio::test]
async fn test_create_and_get_metric() {
    let app = create_test_app().await;

    // Create a metric
    let metric_request = json!({
        "name": "cpu_usage",
        "value": 85.5,
        "tags": {
            "host": "server-01",
            "region": "us-east-1"
        }
    });

    let response = app
        .clone()
        .oneshot(
            Request::builder()
                .method("POST")
                .uri("/api/v1/metrics")
                .header("content-type", "application/json")
                .body(Body::from(metric_request.to_string()))
                .unwrap(),
        )
        .await
        .unwrap();

    assert_eq!(response.status(), StatusCode::OK);

    // Get all metrics
    let response = app
        .oneshot(
            Request::builder()
                .uri("/api/v1/metrics")
                .body(Body::empty())
                .unwrap(),
        )
        .await
        .unwrap();

    assert_eq!(response.status(), StatusCode::OK);
}

#[tokio::test]
async fn test_validation_error() {
    let app = create_test_app().await;

    // Try to create metric with empty name
    let invalid_request = json!({
        "name": "",
        "value": 85.5
    });

    let response = app
        .oneshot(
            Request::builder()
                .method("POST")
                .uri("/api/v1/metrics")
                .header("content-type", "application/json")
                .body(Body::from(invalid_request.to_string()))
                .unwrap(),
        )
        .await
        .unwrap();

    assert_eq!(response.status(), StatusCode::BAD_REQUEST);
}
EOF

print_status "Integration tests created"

# Create .env.example
if [ ! -f ".env.example" ]; then
    print_info "Creating .env.example..."
    cat > .env.example << 'EOF'
# Server Configuration
HOST=127.0.0.1
PORT=8080

# Database Configuration (for future modules)
DATABASE_URL=postgresql://apm_user:apm_password@localhost:5432/apm_monitoring

# Redis Configuration (for future modules)
REDIS_URL=redis://localhost:6379

# JWT Configuration (for future modules)
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production

# Log Level
RUST_LOG=debug
EOF
    print_status ".env.example created"
fi

echo ""
echo "🎉 Module 2 setup completed!"
echo ""
echo "Next steps:"
echo "1. cargo build (compile the project)"
echo "2. cargo test (run tests)"
echo "3. cargo run (start the server)"
echo "4. Test endpoints with curl:"
echo "   curl http://localhost:8080/health"
echo "   curl -X POST http://localhost:8080/api/v1/metrics \\"
echo "     -H 'Content-Type: application/json' \\"
echo "     -d '{\"name\": \"cpu_usage\", \"value\": 85.5}'"
echo ""
echo "📚 Continue with Module 3 to add database persistence!"
